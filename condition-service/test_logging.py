#!/usr/bin/env python3
"""
Test script to demonstrate the logging configuration.
Run this to see how the logging works across different modules.
"""

import os
import sys

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.logging_config import setup_logging, get_logger

def test_logging():
    """Test the logging configuration with different log levels and modules."""
    
    # Setup logging
    setup_logging()
    
    # Get loggers for different modules
    main_logger = get_logger("app.main")
    api_logger = get_logger("app.api")
    models_logger = get_logger("app.models")
    services_logger = get_logger("app.services")
    
    print("Testing logging configuration...")
    print("=" * 50)
    
    # Test different log levels
    main_logger.info("This is an info message from main")
    main_logger.warning("This is a warning message from main")
    main_logger.error("This is an error message from main")
    
    # Test different modules
    api_logger.info("API module is processing a request")
    models_logger.info("Model validation completed successfully")
    services_logger.info("Service operation completed")
    
    # Test structured logging
    user_id = "user123"
    request_id = "req456"
    processing_time = 0.123
    
    api_logger.info(f"Request processed - User: {user_id}, Request ID: {request_id}, Time: {processing_time:.3f}s")
    
    # Test error logging with context
    try:
        raise ValueError("This is a test error")
    except ValueError as e:
        main_logger.error(f"Test error occurred: {str(e)}")
        main_logger.exception("Full traceback for test error:")
    
    print("=" * 50)
    print("Logging test completed. Check the output above to see the formatted logs.")

if __name__ == "__main__":
    test_logging()
