# Logging Configuration for Condition Service

This document describes the comprehensive logging setup implemented across the Condition Service application.

## Overview

The application now includes structured logging throughout all modules with:
- Centralized logging configuration
- Environment-based log levels
- Structured log formatting
- Request tracking and performance monitoring
- Error handling with full context
- Focus on INFO, WARNING, and ERROR levels (no debug logging)

## Configuration

### Environment Variables

- `LOG_LEVEL`: Set the logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL). Default: INFO
- `ENVIRONMENT`: Set the environment type (development, production). Default: development

### Examples

```bash
# Development with debug logging
export LOG_LEVEL=DEBUG
export ENVIRONMENT=development

# Production with info logging
export LOG_LEVEL=INFO
export ENVIRONMENT=production
```

## Log Formats

### Development Format
```
2024-01-15 10:30:45 - app.api.v1.endpoints.predict - INFO - get_therapist_response:25 - 🎨 Starting artwork analysis - Request ID: user123_1705312245
```

### Production Format
```
INFO - app.api.v1.endpoints.predict - 🎨 Starting artwork analysis - Request ID: user123_1705312245
```

## Logging Locations

### Files with Logging

1. **`app/core/logging_config.py`** - Centralized logging configuration
2. **`main.py`** - Application startup/shutdown logging
3. **`app/api/v1/router.py`** - Router setup logging
4. **`app/api/v1/endpoints/predict.py`** - Request/response logging with performance metrics
5. **`app/models/predict_request.py`** - Model validation logging
6. **All `__init__.py` files** - Module initialization logging

### Log Categories

#### Application Lifecycle
- Application startup and shutdown
- Module initialization
- Router configuration

#### Request Processing
- Incoming request details
- Request validation
- Processing time tracking
- Response generation
- Error handling with full context

#### Model Validation
- Field validation attempts
- Validation failures with detailed error context
- Successful validation confirmations

## Key Features

### Request Tracking
Each request gets a unique request ID for tracking:
```python
request_id = f"{r.art_details.user_id}_{int(start_time)}"
```

### Performance Monitoring
Processing time is tracked and logged:
```python
processing_time = time.time() - start_time
logger.info(f"Processing time: {processing_time:.3f} seconds")
```

### Structured Error Logging
Errors include full context and tracebacks:
```python
logger.error(f"❌ Error processing analysis request {request_id}: {str(e)}")
logger.exception("Full error traceback:")
```

### Validation Logging
Model validation includes error logging for failures:
```python
logger.error(f"Invalid condition '{condition}' provided. Allowed conditions: {allowed_conditions}")
```

## Testing the Logging

Run the test script to see logging in action:

```bash
cd condition-service
python test_logging.py
```

## Production Considerations

### File Logging
In production, logs are written to rotating files:
- Location: `logs/condition_service.log`
- Max size: 10MB per file
- Backup count: 5 files
- Encoding: UTF-8

### Log Levels
The application primarily uses:
- **INFO**: General application flow and important events
- **WARNING**: Something unexpected happened
- **ERROR**: Serious problem occurred
- **CRITICAL**: Very serious error occurred

Note: DEBUG level logging has been removed to keep logs focused and clean.

### Performance
- Focused logging (INFO and above) reduces log volume and improves performance
- Structured logging provides consistent format for log analysis
- Request IDs enable tracing across distributed systems

## Integration with Monitoring

The structured logging format makes it easy to integrate with:
- ELK Stack (Elasticsearch, Logstash, Kibana)
- Splunk
- CloudWatch Logs
- Datadog
- Other log aggregation systems

## Best Practices Implemented

1. **Consistent Logger Names**: Using `__name__` for module-specific loggers
2. **Structured Messages**: Consistent format with context
3. **Performance Tracking**: Request timing and metrics
4. **Error Context**: Full error details with request context
5. **Environment Awareness**: Different configs for dev/prod
6. **Security**: No sensitive data in logs
7. **Readability**: Emojis and clear formatting for easy scanning
