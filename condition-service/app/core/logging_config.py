import logging
import logging.config
import sys
from typing import Dict, Any
import os


def get_logging_config() -> Dict[str, Any]:
    """
    Returns a comprehensive logging configuration dictionary.
    
    This configuration sets up:
    - Console handler for development
    - File handler for production logs
    - Different log levels based on environment
    - Structured formatting for better readability
    """
    
    # Get log level from environment variable, default to INFO
    log_level = os.getenv("LOG_LEVEL", "INFO").upper()
    
    # Get environment type
    environment = os.getenv("ENVIRONMENT", "development").lower()
    
    # Base logging configuration
    config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "simple": {
                "format": "%(levelname)s - %(name)s - %(message)s"
            },
            "json": {
                "format": "%(asctime)s %(name)s %(levelname)s %(funcName)s %(lineno)d %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": log_level,
                "formatter": "detailed" if environment == "development" else "simple",
                "stream": sys.stdout
            }
        },
        "loggers": {
            # Application loggers
            "app": {
                "level": log_level,
                "handlers": ["console"],
                "propagate": False
            },
            "app.api": {
                "level": log_level,
                "handlers": ["console"],
                "propagate": False
            },
            "app.models": {
                "level": log_level,
                "handlers": ["console"],
                "propagate": False
            },
            "app.services": {
                "level": log_level,
                "handlers": ["console"],
                "propagate": False
            },
            # FastAPI and Uvicorn loggers
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False
            },
            "fastapi": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False
            }
        },
        "root": {
            "level": log_level,
            "handlers": ["console"]
        }
    }
    
    # Add file handler for production
    if environment == "production":
        config["handlers"]["file"] = {
            "class": "logging.handlers.RotatingFileHandler",
            "level": log_level,
            "formatter": "json",
            "filename": "logs/condition_service.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "encoding": "utf8"
        }
        
        # Add file handler to all loggers
        for logger_name in config["loggers"]:
            config["loggers"][logger_name]["handlers"].append("file")
        config["root"]["handlers"].append("file")
    
    return config


def setup_logging():
    """
    Set up logging configuration for the application.
    
    This function should be called once at application startup.
    """
    # Create logs directory if it doesn't exist (for production)
    os.makedirs("logs", exist_ok=True)
    
    # Get and apply logging configuration
    config = get_logging_config()
    logging.config.dictConfig(config)
    
    # Get the root logger and log startup message
    logger = logging.getLogger("app")
    logger.info("Logging configuration initialized successfully")
    logger.info(f"Log level set to: {os.getenv('LOG_LEVEL', 'INFO')}")
    logger.info(f"Environment: {os.getenv('ENVIRONMENT', 'development')}")


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.
    
    Args:
        name: The name for the logger (typically __name__)
        
    Returns:
        A configured logger instance
    """
    return logging.getLogger(name)
