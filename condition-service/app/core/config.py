import os
from typing import Optional

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    s3_url: Optional[str] = os.environ.get('S3_URL', None)
    aws_access_key_id: Optional[str] = os.environ.get('AWS_ACCESS_KEY_ID', None)
    aws_secret_access_key: Optional[str] = os.environ.get('AWS_SECRET_ACCESS_KEY', None)

    class Config:
        env_file = '.env'


settings = Settings()