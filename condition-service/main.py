from contextlib import asynccontextmanager
from fastapi import FastAPI

from app.api.v1.router import api_router
from app.core.logging_config import setup_logging, get_logger

# Initialize logging configuration
setup_logging()
logger = get_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Lifespan context manager for FastAPI application.
    Handles startup and shutdown events.
    """
    # Startup
    logger.info("🚀 Condition Service is starting up...")
    logger.info("FastAPI application initialized")
    logger.info("API version: 0.0.1")

    yield

    # Shutdown
    logger.info("🛑 Condition Service is shutting down...")
    logger.info("Cleanup completed")

app = FastAPI(
    title="Condition Service",
    version="0.0.1",
    lifespan=lifespan
)

# Include API router
logger.info("Including API v1 router with prefix /api/v1")
app.include_router(api_router, prefix="/api/v1")
logger.info("✅ Application setup completed successfully")
